const std = @import("std");
const testing = std.testing;

/// Lens - 函数式数据访问和修改
pub fn Lens(comptime S: type, comptime A: type) type {
    return struct {
        const Self = @This();

        get_fn: *const fn (S) A,
        set_fn: *const fn (S, A) S,

        pub fn init(get_fn: *const fn (S) A, set_fn: *const fn (S, A) S) Self {
            return Self{
                .get_fn = get_fn,
                .set_fn = set_fn,
            };
        }

        /// 获取值
        pub fn get(self: Self, s: S) A {
            return self.get_fn(s);
        }

        /// 设置值
        pub fn set(self: Self, s: S, a: A) S {
            return self.set_fn(s, a);
        }

        /// 修改值
        pub fn modify(self: Self, s: S, comptime func: *const fn (A) A) S {
            const current = self.get(s);
            const new_value = func(current);
            return self.set(s, new_value);
        }

        /// Lens 组合 - 简化实现避免闭包问题
        pub fn composeWith(comptime B: type, lens1: Lens(S, A), lens2: Lens(A, B)) Lens(S, B) {
            return Lens(S, B).init(struct {
                const l1 = lens1;
                const l2 = lens2;
                fn composed_get(s: S) B {
                    const a = l1.get(s);
                    return l2.get(a);
                }
            }.composed_get, struct {
                const l1 = lens1;
                const l2 = lens2;
                fn composed_set(s: S, b: B) S {
                    const a = l1.get(s);
                    const new_a = l2.set(a, b);
                    return l1.set(s, new_a);
                }
            }.composed_set);
        }

        /// 创建字段 Lens
        pub fn field(comptime field_name: []const u8) Lens(S, @TypeOf(@field(@as(S, undefined), field_name))) {
            const FieldType = @TypeOf(@field(@as(S, undefined), field_name));
            return Lens(S, FieldType).init(struct {
                fn get_field(s: S) FieldType {
                    return @field(s, field_name);
                }
            }.get_field, struct {
                fn set_field(s: S, value: FieldType) S {
                    var result = s;
                    @field(result, field_name) = value;
                    return result;
                }
            }.set_field);
        }
    };
}

/// Prism - 处理 sum 类型的 optic
pub fn Prism(comptime S: type, comptime A: type) type {
    return struct {
        const Self = @This();

        preview_fn: *const fn (S) ?A,
        review_fn: *const fn (A) S,

        pub fn init(preview_fn: *const fn (S) ?A, review_fn: *const fn (A) S) Self {
            return Self{
                .preview_fn = preview_fn,
                .review_fn = review_fn,
            };
        }

        /// 尝试获取值
        pub fn preview(self: Self, s: S) ?A {
            return self.preview_fn(s);
        }

        /// 从值构造
        pub fn review(self: Self, a: A) S {
            return self.review_fn(a);
        }

        /// 修改值（如果存在）
        pub fn modify(self: Self, s: S, comptime func: *const fn (A) A) S {
            if (self.preview(s)) |a| {
                return self.review(func(a));
            }
            return s;
        }

        /// 设置值（如果存在）
        pub fn set(self: Self, s: S, a: A) S {
            if (self.preview(s)) |_| {
                return self.review(a);
            }
            return s;
        }
    };
}

/// Traversal - 处理多个焦点的 optic
pub fn Traversal(comptime S: type, comptime A: type) type {
    return struct {
        const Self = @This();

        traverse_fn: *const fn (S, *const fn (A) A) S,
        to_list_fn: *const fn (S, std.mem.Allocator) []A,

        pub fn init(traverse_fn: *const fn (S, *const fn (A) A) S, to_list_fn: *const fn (S, std.mem.Allocator) []A) Self {
            return Self{
                .traverse_fn = traverse_fn,
                .to_list_fn = to_list_fn,
            };
        }

        /// 修改所有焦点
        pub fn modify(self: Self, s: S, comptime func: *const fn (A) A) S {
            return self.traverse_fn(s, func);
        }

        /// 获取所有焦点
        pub fn toList(self: Self, s: S, allocator: std.mem.Allocator) []A {
            return self.to_list_fn(s, allocator);
        }

        /// 设置所有焦点
        pub fn set(self: Self, s: S, a: A) S {
            return self.modify(s, struct {
                const value = a;
                fn set_value(_: A) A {
                    return value;
                }
            }.set_value);
        }
    };
}

/// Iso - 同构，表示两个类型之间的双向转换
pub fn Iso(comptime S: type, comptime A: type) type {
    return struct {
        const Self = @This();

        from_fn: *const fn (S) A,
        to_fn: *const fn (A) S,

        pub fn init(from_fn: *const fn (S) A, to_fn: *const fn (A) S) Self {
            return Self{
                .from_fn = from_fn,
                .to_fn = to_fn,
            };
        }

        /// 正向转换
        pub fn from(self: Self, s: S) A {
            return self.from_fn(s);
        }

        /// 反向转换
        pub fn to(self: Self, a: A) S {
            return self.to_fn(a);
        }

        /// 反转 Iso
        pub fn reverse(self: Self) Iso(A, S) {
            return Iso(A, S).init(self.to_fn, self.from_fn);
        }

        /// 转换为 Lens
        pub fn asLens(comptime self: Self) Lens(S, A) {
            return Lens(S, A).init(self.from_fn, struct {
                fn set_via_iso(_: S, a: A) S {
                    return self.to_fn(a);
                }
            }.set_via_iso);
        }
    };
}

/// 实用的 Lens 构造函数
pub const LensBuilder = struct {
    /// 为结构体字段创建 Lens
    pub fn fieldLens(comptime S: type, comptime field_name: []const u8) Lens(S, @TypeOf(@field(@as(S, undefined), field_name))) {
        return Lens(S, @TypeOf(@field(@as(S, undefined), field_name))).field(field_name);
    }

    /// 为数组索引创建 Lens
    pub fn indexLens(comptime T: type, index: usize) Lens([]T, T) {
        return Lens([]T, T).init(struct {
            const idx = index;
            fn get_index(arr: []T) T {
                return arr[idx];
            }
        }.get_index, struct {
            const idx = index;
            fn set_index(arr: []T, value: T) []T {
                var result = arr;
                result[idx] = value;
                return result;
            }
        }.set_index);
    }
};

/// 独立的 Lens 组合函数
pub fn composeWith(comptime S: type, comptime A: type, comptime B: type, comptime lens1: Lens(S, A), comptime lens2: Lens(A, B)) Lens(S, B) {
    return Lens(S, B).init(struct {
        fn composed_get(s: S) B {
            const a = lens1.get(s);
            return lens2.get(a);
        }
    }.composed_get, struct {
        fn composed_set(s: S, b: B) S {
            const a = lens1.get(s);
            const new_a = lens2.set(a, b);
            return lens1.set(s, new_a);
        }
    }.composed_set);
}

// 测试用的数据结构
const Person = struct {
    name: []const u8,
    age: u32,
    address: Address,
};

const Address = struct {
    street: []const u8,
    city: []const u8,
    zip: u32,
};

// 测试
test "Lens 基本操作" {
    const person = Person{
        .name = "Alice",
        .age = 30,
        .address = Address{
            .street = "123 Main St",
            .city = "Springfield",
            .zip = 12345,
        },
    };

    // 创建 name lens
    const name_lens = LensBuilder.fieldLens(Person, "name");

    // 获取值
    try testing.expectEqualStrings("Alice", name_lens.get(person));

    // 设置值
    const updated_person = name_lens.set(person, "Bob");
    try testing.expectEqualStrings("Bob", updated_person.name);
    try testing.expectEqual(@as(u32, 30), updated_person.age); // 其他字段不变
}

test "Lens 组合" {
    const person = Person{
        .name = "Alice",
        .age = 30,
        .address = Address{
            .street = "123 Main St",
            .city = "Springfield",
            .zip = 12345,
        },
    };

    // 创建组合 lens: person.address.city
    const address_lens = LensBuilder.fieldLens(Person, "address");
    const city_lens = LensBuilder.fieldLens(Address, "city");
    const person_city_lens = composeWith(Person, Address, []const u8, address_lens, city_lens);

    // 获取城市
    try testing.expectEqualStrings("Springfield", person_city_lens.get(person));

    // 修改城市
    const updated_person = person_city_lens.set(person, "New York");
    try testing.expectEqualStrings("New York", updated_person.address.city);
    try testing.expectEqualStrings("Alice", updated_person.name); // 其他字段不变
}

test "Prism 操作" {
    const Maybe = union(enum) {
        Nothing: void,
        Just: i32,
    };

    // 创建 Just prism
    const just_prism = Prism(Maybe, i32).init(struct {
        fn preview(maybe: Maybe) ?i32 {
            return switch (maybe) {
                .Just => |value| value,
                .Nothing => null,
            };
        }
    }.preview, struct {
        fn review(value: i32) Maybe {
            return Maybe{ .Just = value };
        }
    }.review);

    const just_5 = Maybe{ .Just = 5 };
    const nothing = Maybe{ .Nothing = {} };

    // 测试 preview
    try testing.expectEqual(@as(?i32, 5), just_prism.preview(just_5));
    try testing.expectEqual(@as(?i32, null), just_prism.preview(nothing));

    // 测试 review
    const new_just = just_prism.review(10);
    try testing.expectEqual(@as(i32, 10), just_prism.preview(new_just).?);

    // 测试 modify
    const double = struct {
        fn f(x: i32) i32 {
            return x * 2;
        }
    }.f;

    const doubled_just = just_prism.modify(just_5, double);
    try testing.expectEqual(@as(i32, 10), just_prism.preview(doubled_just).?);

    const doubled_nothing = just_prism.modify(nothing, double);
    try testing.expectEqual(@as(?i32, null), just_prism.preview(doubled_nothing));
}

test "Iso 操作" {
    // 字符串和字节数组之间的同构
    const string_bytes_iso = Iso([]const u8, []const u8).init(struct {
        fn from(s: []const u8) []const u8 {
            return s; // 简化示例
        }
    }.from, struct {
        fn to(bytes: []const u8) []const u8 {
            return bytes; // 简化示例
        }
    }.to);

    const original = "hello";
    const converted = string_bytes_iso.from(original);
    const back = string_bytes_iso.to(converted);

    try testing.expectEqualStrings(original, back);

    // 转换为 Lens
    const as_lens = string_bytes_iso.asLens();
    try testing.expectEqualStrings("hello", as_lens.get("hello"));
}
