const std = @import("std");
const testing = std.testing;
const Allocator = std.mem.Allocator;
const UniquePtr = @import("memory_management.zig").UniquePtr;
const SharedPtr = @import("memory_management.zig").SharedPtr;

/// 函数式链表 - 使用智能指针管理内存
pub fn FunctionalList(comptime T: type) type {
    return union(enum) {
        const Self = @This();

        Nil: void,
        Cons: struct {
            head: T,
            tail: SharedPtr(Self),
        },

        /// 创建空列表
        pub fn nil() Self {
            return Self{ .Nil = {} };
        }

        /// 创建非空列表
        pub fn cons(allocator: Allocator, head: T, tail: Self) !Self {
            const tail_ptr = try SharedPtr(Self).init(allocator, tail);
            return Self{ .Cons = .{ .head = head, .tail = tail_ptr } };
        }

        /// 从数组创建列表
        pub fn fromSlice(allocator: Allocator, items: []const T) !Self {
            var result = Self.nil();
            var i = items.len;
            while (i > 0) {
                i -= 1;
                result = try Self.cons(allocator, items[i], result);
            }
            return result;
        }

        /// 转换为数组
        pub fn toSlice(self: Self, allocator: Allocator) ![]T {
            var result = std.ArrayList(T).init(allocator);
            defer result.deinit();

            var current = self;
            while (true) {
                switch (current) {
                    .Nil => break,
                    .Cons => |cons_data| {
                        try result.append(cons_data.head);
                        if (cons_data.tail.get()) |tail_ptr| {
                            current = tail_ptr.*;
                        } else {
                            break;
                        }
                    },
                }
            }

            return result.toOwnedSlice();
        }

        /// 获取长度
        pub fn length(self: Self) usize {
            return switch (self) {
                .Nil => 0,
                .Cons => |cons_data| {
                    if (cons_data.tail.get()) |tail_ptr| {
                        return 1 + tail_ptr.length();
                    } else {
                        return 1;
                    }
                },
            };
        }

        /// Map 操作
        pub fn map(self: Self, comptime U: type, allocator: Allocator, comptime func: *const fn (T) U) !FunctionalList(U) {
            return switch (self) {
                .Nil => FunctionalList(U).nil(),
                .Cons => |cons_data| {
                    const mapped_tail = if (cons_data.tail.get()) |tail_ptr|
                        try tail_ptr.map(U, allocator, func)
                    else
                        FunctionalList(U).nil();

                    return FunctionalList(U).cons(allocator, func(cons_data.head), mapped_tail);
                },
            };
        }

        /// Filter 操作
        pub fn filter(self: Self, allocator: Allocator, comptime predicate: *const fn (T) bool) !Self {
            return switch (self) {
                .Nil => Self.nil(),
                .Cons => |cons_data| {
                    const filtered_tail = if (cons_data.tail.get()) |tail_ptr|
                        try tail_ptr.filter(allocator, predicate)
                    else
                        Self.nil();

                    if (predicate(cons_data.head)) {
                        return Self.cons(allocator, cons_data.head, filtered_tail);
                    } else {
                        return filtered_tail;
                    }
                },
            };
        }

        /// Fold 操作
        pub fn fold(self: Self, comptime U: type, initial: U, comptime func: *const fn (U, T) U) U {
            return switch (self) {
                .Nil => initial,
                .Cons => |cons_data| {
                    const new_acc = func(initial, cons_data.head);
                    if (cons_data.tail.get()) |tail_ptr| {
                        return tail_ptr.fold(U, new_acc, func);
                    } else {
                        return new_acc;
                    }
                },
            };
        }

        /// 连接两个列表
        pub fn append(self: Self, allocator: Allocator, other: Self) !Self {
            return switch (self) {
                .Nil => other,
                .Cons => |cons_data| {
                    const appended_tail = if (cons_data.tail.get()) |tail_ptr|
                        try tail_ptr.append(allocator, other)
                    else
                        other;

                    return Self.cons(allocator, cons_data.head, appended_tail);
                },
            };
        }

        /// 反转列表
        pub fn reverse(self: Self, allocator: Allocator) !Self {
            return self.reverseHelper(allocator, Self.nil());
        }

        fn reverseHelper(self: Self, allocator: Allocator, acc: Self) !Self {
            return switch (self) {
                .Nil => acc,
                .Cons => |cons_data| {
                    const new_acc = try Self.cons(allocator, cons_data.head, acc);
                    if (cons_data.tail.get()) |tail_ptr| {
                        return tail_ptr.reverseHelper(allocator, new_acc);
                    } else {
                        return new_acc;
                    }
                },
            };
        }

        /// 释放内存
        pub fn deinit(self: *Self) void {
            switch (self.*) {
                .Nil => {},
                .Cons => |*cons_data| {
                    cons_data.tail.deinit();
                },
            }
        }
    };
}

/// 函数式二叉树
pub fn FunctionalTree(comptime T: type) type {
    return union(enum) {
        const Self = @This();

        Leaf: T,
        Node: struct {
            left: SharedPtr(Self),
            right: SharedPtr(Self),
        },

        /// 创建叶子节点
        pub fn leaf(value: T) Self {
            return Self{ .Leaf = value };
        }

        /// 创建内部节点
        pub fn node(allocator: Allocator, left: Self, right: Self) !Self {
            const left_ptr = try SharedPtr(Self).init(allocator, left);
            const right_ptr = try SharedPtr(Self).init(allocator, right);
            return Self{ .Node = .{ .left = left_ptr, .right = right_ptr } };
        }

        /// Map 操作
        pub fn map(self: Self, comptime U: type, allocator: Allocator, comptime func: *const fn (T) U) !FunctionalTree(U) {
            return switch (self) {
                .Leaf => |value| FunctionalTree(U).leaf(func(value)),
                .Node => |node_data| {
                    const mapped_left = if (node_data.left.get()) |left_ptr|
                        try left_ptr.map(U, allocator, func)
                    else
                        return error.InvalidTree;

                    const mapped_right = if (node_data.right.get()) |right_ptr|
                        try right_ptr.map(U, allocator, func)
                    else
                        return error.InvalidTree;

                    return FunctionalTree(U).node(allocator, mapped_left, mapped_right);
                },
            };
        }

        /// Fold 操作
        pub fn fold(self: Self, comptime U: type, comptime leaf_func: *const fn (T) U, comptime node_func: *const fn (U, U) U) U {
            return switch (self) {
                .Leaf => |value| leaf_func(value),
                .Node => |node_data| {
                    const left_result = if (node_data.left.get()) |left_ptr|
                        left_ptr.fold(U, leaf_func, node_func)
                    else
                        @panic("Invalid tree structure");

                    const right_result = if (node_data.right.get()) |right_ptr|
                        right_ptr.fold(U, leaf_func, node_func)
                    else
                        @panic("Invalid tree structure");

                    return node_func(left_result, right_result);
                },
            };
        }

        /// 计算树的大小
        pub fn size(self: Self) usize {
            return self.fold(usize, struct {
                fn leaf_size(_: T) usize {
                    return 1;
                }
            }.leaf_size, struct {
                fn node_size(left: usize, right: usize) usize {
                    return left + right;
                }
            }.node_size);
        }

        /// 计算树的深度
        pub fn depth(self: Self) usize {
            return self.fold(usize, struct {
                fn leaf_depth(_: T) usize {
                    return 1;
                }
            }.leaf_depth, struct {
                fn node_depth(left: usize, right: usize) usize {
                    return 1 + @max(left, right);
                }
            }.node_depth);
        }

        /// 中序遍历
        pub fn inorder(self: Self, allocator: Allocator) ![]T {
            var result = std.ArrayList(T).init(allocator);
            defer result.deinit();

            try self.inorderHelper(&result);
            return result.toOwnedSlice();
        }

        fn inorderHelper(self: Self, result: *std.ArrayList(T)) !void {
            switch (self) {
                .Leaf => |value| try result.append(value),
                .Node => |node_data| {
                    if (node_data.left.get()) |left_ptr| {
                        try left_ptr.inorderHelper(result);
                    }
                    if (node_data.right.get()) |right_ptr| {
                        try right_ptr.inorderHelper(result);
                    }
                },
            }
        }

        /// 释放内存
        pub fn deinit(self: *Self) void {
            switch (self.*) {
                .Leaf => {},
                .Node => |*node_data| {
                    node_data.left.deinit();
                    node_data.right.deinit();
                },
            }
        }
    };
}

/// Rose Tree (多叉树)
pub fn RoseTree(comptime T: type) type {
    return struct {
        const Self = @This();

        value: T,
        children: std.ArrayList(SharedPtr(Self)),
        allocator: Allocator,

        pub fn init(allocator: Allocator, value: T) Self {
            return Self{
                .value = value,
                .children = std.ArrayList(SharedPtr(Self)).init(allocator),
                .allocator = allocator,
            };
        }

        pub fn addChild(self: *Self, child: Self) !void {
            const child_ptr = try SharedPtr(Self).init(self.allocator, child);
            try self.children.append(child_ptr);
        }

        pub fn map(self: Self, comptime U: type, comptime func: *const fn (T) U) !RoseTree(U) {
            var result = RoseTree(U).init(self.allocator, func(self.value));

            for (self.children.items) |child_ptr| {
                if (child_ptr.get()) |child| {
                    const mapped_child = try child.map(U, func);
                    try result.addChild(mapped_child);
                }
            }

            return result;
        }

        pub fn fold(self: Self, comptime U: type, comptime func: *const fn (T, []const U) U) !U {
            var child_results = std.ArrayList(U).init(self.allocator);
            defer child_results.deinit();

            for (self.children.items) |child_ptr| {
                if (child_ptr.get()) |child| {
                    const child_result = try child.fold(U, func);
                    try child_results.append(child_result);
                }
            }

            return func(self.value, child_results.items);
        }

        pub fn deinit(self: *Self) void {
            for (self.children.items) |*child_ptr| {
                child_ptr.deinit();
            }
            self.children.deinit();
        }
    };
}

// 测试
test "函数式链表" {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();

    // 创建列表 [1, 2, 3]
    var list = try FunctionalList(i32).fromSlice(allocator, &[_]i32{ 1, 2, 3 });
    defer list.deinit();

    // 测试长度
    try testing.expectEqual(@as(usize, 3), list.length());

    // 测试 map
    const double = struct {
        fn f(x: i32) i32 {
            return x * 2;
        }
    }.f;

    var doubled = try list.map(i32, allocator, double);
    defer doubled.deinit();

    const doubled_slice = try doubled.toSlice(allocator);
    defer allocator.free(doubled_slice);

    try testing.expectEqual(@as(i32, 2), doubled_slice[0]);
    try testing.expectEqual(@as(i32, 6), doubled_slice[2]);

    // 注意：由于 SharedPtr 的引用计数，可能会有内存泄漏
    // 这是简化实现的限制
}

test "函数式二叉树" {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();

    // 创建树: Node(Leaf(1), Node(Leaf(2), Leaf(3)))
    const left = FunctionalTree(i32).leaf(1);
    const right_left = FunctionalTree(i32).leaf(2);
    const right_right = FunctionalTree(i32).leaf(3);
    var right = try FunctionalTree(i32).node(allocator, right_left, right_right);
    defer right.deinit();

    var tree = try FunctionalTree(i32).node(allocator, left, right);
    defer tree.deinit();

    // 测试大小
    try testing.expectEqual(@as(usize, 3), tree.size());

    // 测试深度
    try testing.expectEqual(@as(usize, 3), tree.depth());

    // 测试 fold (求和)
    const sum = tree.fold(i32, struct {
        fn identity(x: i32) i32 {
            return x;
        }
    }.identity, struct {
        fn add(left_val: i32, right_val: i32) i32 {
            return left_val + right_val;
        }
    }.add);

    try testing.expectEqual(@as(i32, 6), sum); // 1 + 2 + 3
}
