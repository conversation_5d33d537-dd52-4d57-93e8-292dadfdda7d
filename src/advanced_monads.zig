const std = @import("std");
const testing = std.testing;
const Allocator = std.mem.Allocator;

/// State 单子 - 使用闭包解决方案
pub fn State(comptime S: type, comptime A: type) type {
    return struct {
        const Self = @This();

        const StateFunction = struct {
            run_fn: *const fn (*@This(), S) struct { value: A, state: S },
            context: *anyopaque,

            pub fn run(self: *@This(), state: S) struct { value: A, state: S } {
                return self.run_fn(self, state);
            }
        };

        state_func: StateFunction,
        allocator: Allocator,

        pub fn init(allocator: Allocator, comptime Context: type, context: Context, comptime run_fn: *const fn (Context, S) struct { value: A, state: S }) !Self {
            const context_ptr = try allocator.create(Context);
            context_ptr.* = context;

            return Self{
                .state_func = StateFunction{
                    .run_fn = struct {
                        fn wrapper(state_func: *StateFunction, state: S) struct { value: A, state: S } {
                            const typed_ctx: *Context = @ptrCast(@alignCast(state_func.context));
                            return run_fn(typed_ctx.*, state);
                        }
                    }.wrapper,
                    .context = context_ptr,
                },
                .allocator = allocator,
            };
        }

        pub fn pure(allocator: Allocator, value: A) !Self {
            return Self.init(allocator, A, value, struct {
                fn run(val: A, state: S) struct { value: A, state: S } {
                    return .{ .value = val, .state = state };
                }
            }.run);
        }

        pub fn deinit(self: Self) void {
            // 简化的内存管理
            // 实际实现中需要知道原始类型来正确释放
        }

        pub fn runState(self: *Self, initial_state: S) struct { value: A, state: S } {
            return self.state_func.run(initial_state);
        }

        pub fn evalState(self: *Self, initial_state: S) A {
            return self.runState(initial_state).value;
        }

        pub fn execState(self: *Self, initial_state: S) S {
            return self.runState(initial_state).state;
        }

        pub fn map(self: Self, comptime B: type, comptime func: *const fn (A) B) !State(S, B) {
            const MapContext = struct {
                original: State(S, A),
                map_func: *const fn (A) B,
            };

            return State(S, B).init(self.allocator, MapContext, MapContext{
                .original = self,
                .map_func = func,
            }, struct {
                fn run(ctx: MapContext, state: S) struct { value: B, state: S } {
                    var original_copy = ctx.original;
                    const result = original_copy.runState(state);
                    return .{
                        .value = ctx.map_func(result.value),
                        .state = result.state,
                    };
                }
            }.run);
        }

        pub fn flatMap(self: Self, comptime B: type, comptime func: *const fn (A) State(S, B)) !State(S, B) {
            const FlatMapContext = struct {
                original: State(S, A),
                flat_func: *const fn (A) State(S, B),
            };

            return State(S, B).init(self.allocator, FlatMapContext, FlatMapContext{
                .original = self,
                .flat_func = func,
            }, struct {
                fn run(ctx: FlatMapContext, state: S) struct { value: B, state: S } {
                    var original_copy = ctx.original;
                    const result1 = original_copy.runState(state);
                    var state2 = ctx.flat_func(result1.value);
                    return state2.runState(result1.state);
                }
            }.run);
        }
    };
}

/// State 单子的辅助函数
pub fn StateMonad(comptime S: type) type {
    return struct {
        pub fn get(allocator: Allocator) !State(S, S) {
            return State(S, S).init(allocator, void, {}, struct {
                fn run(_: void, state: S) struct { value: S, state: S } {
                    return .{ .value = state, .state = state };
                }
            }.run);
        }

        pub fn put(allocator: Allocator, new_state: S) !State(S, void) {
            return State(S, void).init(allocator, S, new_state, struct {
                fn run(ns: S, _: S) struct { value: void, state: S } {
                    return .{ .value = {}, .state = ns };
                }
            }.run);
        }

        pub fn modify(allocator: Allocator, comptime func: *const fn (S) S) !State(S, void) {
            return State(S, void).init(allocator, *const fn (S) S, func, struct {
                fn run(f: *const fn (S) S, state: S) struct { value: void, state: S } {
                    return .{ .value = {}, .state = f(state) };
                }
            }.run);
        }
    };
}

/// Reader 单子
pub fn Reader(comptime R: type, comptime A: type) type {
    return struct {
        const Self = @This();

        const ReaderFunction = struct {
            run_fn: *const fn (*@This(), R) A,
            context: *anyopaque,

            pub fn run(self: *@This(), env: R) A {
                return self.run_fn(self, env);
            }
        };

        reader_func: ReaderFunction,
        allocator: Allocator,

        pub fn init(allocator: Allocator, comptime Context: type, context: Context, comptime run_fn: *const fn (Context, R) A) !Self {
            const context_ptr = try allocator.create(Context);
            context_ptr.* = context;

            return Self{
                .reader_func = ReaderFunction{
                    .run_fn = struct {
                        fn wrapper(reader_func: *ReaderFunction, env: R) A {
                            const typed_ctx: *Context = @ptrCast(@alignCast(reader_func.context));
                            return run_fn(typed_ctx.*, env);
                        }
                    }.wrapper,
                    .context = context_ptr,
                },
                .allocator = allocator,
            };
        }

        pub fn pure(allocator: Allocator, value: A) !Self {
            return Self.init(allocator, A, value, struct {
                fn run(val: A, _: R) A {
                    return val;
                }
            }.run);
        }

        pub fn runReader(self: *Self, env: R) A {
            return self.reader_func.run(env);
        }

        pub fn map(self: Self, comptime B: type, comptime func: *const fn (A) B) !Reader(R, B) {
            const MapContext = struct {
                original: Reader(R, A),
                map_func: *const fn (A) B,
            };

            return Reader(R, B).init(self.allocator, MapContext, MapContext{
                .original = self,
                .map_func = func,
            }, struct {
                fn run(ctx: MapContext, env: R) B {
                    var original_copy = ctx.original;
                    return ctx.map_func(original_copy.runReader(env));
                }
            }.run);
        }

        pub fn flatMap(self: Self, comptime B: type, comptime func: *const fn (A) Reader(R, B)) !Reader(R, B) {
            const FlatMapContext = struct {
                original: Reader(R, A),
                flat_func: *const fn (A) Reader(R, B),
            };

            return Reader(R, B).init(self.allocator, FlatMapContext, FlatMapContext{
                .original = self,
                .flat_func = func,
            }, struct {
                fn run(ctx: FlatMapContext, env: R) B {
                    var original_copy = ctx.original;
                    const a = original_copy.runReader(env);
                    var reader_b = ctx.flat_func(a);
                    return reader_b.runReader(env);
                }
            }.run);
        }
    };
}

/// Reader 单子的辅助函数
pub fn ReaderMonad(comptime R: type) type {
    return struct {
        pub fn ask(allocator: Allocator) !Reader(R, R) {
            return Reader(R, R).init(allocator, void, {}, struct {
                fn run(_: void, env: R) R {
                    return env;
                }
            }.run);
        }

        pub fn local(allocator: Allocator, comptime A: type, comptime func: *const fn (R) R, reader: Reader(R, A)) !Reader(R, A) {
            const LocalContext = struct {
                original: Reader(R, A),
                local_func: *const fn (R) R,
            };

            return Reader(R, A).init(allocator, LocalContext, LocalContext{
                .original = reader,
                .local_func = func,
            }, struct {
                fn run(ctx: LocalContext, env: R) A {
                    var original_copy = ctx.original;
                    return original_copy.runReader(ctx.local_func(env));
                }
            }.run);
        }
    };
}

/// Writer 单子
pub fn Writer(comptime W: type, comptime A: type) type {
    return struct {
        const Self = @This();

        value: A,
        log: W,

        pub fn init(value: A, log: W) Self {
            return Self{ .value = value, .log = log };
        }

        pub fn runWriter(self: Self) struct { value: A, log: W } {
            return .{ .value = self.value, .log = self.log };
        }

        pub fn evalWriter(self: Self) A {
            return self.value;
        }

        pub fn execWriter(self: Self) W {
            return self.log;
        }

        pub fn map(self: Self, comptime B: type, comptime func: *const fn (A) B) Writer(W, B) {
            return Writer(W, B).init(func(self.value), self.log);
        }

        pub fn flatMap(self: Self, comptime B: type, comptime func: *const fn (A) Writer(W, B), comptime append_func: *const fn (W, W) W) Writer(W, B) {
            const writer_b = func(self.value);
            return Writer(W, B).init(writer_b.value, append_func(self.log, writer_b.log));
        }
    };
}

/// Writer 单子的辅助函数
pub fn WriterMonad(comptime W: type) type {
    return struct {
        pub fn pure(comptime A: type, value: A, empty_log: W) Writer(W, A) {
            return Writer(W, A).init(value, empty_log);
        }

        pub fn tell(log: W) Writer(W, void) {
            return Writer(W, void).init({}, log);
        }
    };
}

/// 简化的 IO 单子
pub const IO = struct {
    const Self = @This();

    action: *const fn () void,

    pub fn init(action: *const fn () void) Self {
        return Self{ .action = action };
    }

    pub fn run(self: Self) void {
        self.action();
    }

    pub fn map(self: Self, comptime func: *const fn () void) Self {
        return Self.init(struct {
            const original_action = self.action;
            const map_func = func;
            fn combined() void {
                original_action();
                map_func();
            }
        }.combined);
    }

    pub fn flatMap(self: Self, comptime func: *const fn () Self) Self {
        return Self.init(struct {
            const original_action = self.action;
            const flat_func = func;
            fn combined() void {
                original_action();
                const next_io = flat_func();
                next_io.run();
            }
        }.combined);
    }
};

// 测试
test "State 单子" {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();

    const Counter = StateMonad(i32);

    const increment = struct {
        fn f(x: i32) i32 {
            return x + 1;
        }
    }.f;

    var modify_state = try Counter.modify(allocator, increment);
    defer modify_state.deinit();

    var get_state = try Counter.get(allocator);
    defer get_state.deinit();

    // 组合操作
    var combined = try modify_state.flatMap(void, struct {
        fn next(_: void) !State(i32, i32) {
            return Counter.get(allocator);
        }
    }.next);
    defer combined.deinit();

    const result = combined.runState(0);
    try testing.expectEqual(@as(i32, 1), result.value);
    try testing.expectEqual(@as(i32, 1), result.state);
}

test "Reader 单子" {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();

    const Config = struct {
        multiplier: i32,
        base: i32,
    };

    const ConfigReader = ReaderMonad(Config);

    var ask_reader = try ConfigReader.ask(allocator);
    defer ask_reader.deinit();

    var computation = try ask_reader.map(i32, struct {
        fn compute(config: Config) i32 {
            return config.base * config.multiplier;
        }
    }.compute);
    defer computation.deinit();

    const config = Config{ .multiplier = 3, .base = 10 };
    const result = computation.runReader(config);
    try testing.expectEqual(@as(i32, 30), result);
}

test "Writer 单子" {
    const StringWriter = WriterMonad([]const u8);

    const computation1 = StringWriter.pure(i32, 5, "");
    const computation2 = StringWriter.tell("计算开始\n");

    const append_strings = struct {
        fn append(a: []const u8, b: []const u8) []const u8 {
            // 简化实现
            _ = a;
            return b;
        }
    }.append;

    const combined = computation1.flatMap(i32, struct {
        fn next(value: i32) Writer([]const u8, i32) {
            return StringWriter.tell("计算完成\n").map(i32, struct {
                const val = value;
                fn final(_: void) i32 {
                    return val * 2;
                }
            }.final);
        }
    }.next, append_strings);

    const result = combined.runWriter();
    try testing.expectEqual(@as(i32, 10), result.value);
}
