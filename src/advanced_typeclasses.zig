const std = @import("std");
const testing = std.testing;
const Allocator = std.mem.Allocator;
const Maybe = @import("maybe.zig").Maybe;
const Either = @import("either.zig").Either;

/// Applicative 函子类型类
pub fn Applicative(comptime F: fn (type) type) type {
    return struct {
        /// pure :: a -> f a
        pub fn pure(comptime A: type, value: A) F(A) {
            return F(A).just(value);
        }

        /// apply :: f (a -> b) -> f a -> f b
        pub fn apply(comptime A: type, comptime B: type, ff: F(fn (A) B), fa: F(A)) F(B) {
            return ff.flatMap(fn (A) B, struct {
                fn apply_func(func: fn (A) B) F(B) {
                    return fa.map(B, func);
                }
            }.apply_func);
        }

        /// liftA2 :: (a -> b -> c) -> f a -> f b -> f c
        pub fn liftA2(comptime A: type, comptime B: type, comptime C: type, comptime func: *const fn (A, B) C, fa: F(A), fb: F(B)) F(C) {
            const curried = fa.map(fn (B) C, struct {
                fn curry(a: A) fn (B) C {
                    return struct {
                        const val_a = a;
                        fn inner(b: B) C {
                            return func(val_a, b);
                        }
                    }.inner;
                }
            }.curry);

            return apply(B, C, curried, fb);
        }

        /// liftA3 :: (a -> b -> c -> d) -> f a -> f b -> f c -> f d
        pub fn liftA3(comptime A: type, comptime B: type, comptime C: type, comptime D: type, comptime func: *const fn (A, B, C) D, fa: F(A), fb: F(B), fc: F(C)) F(D) {
            const step1 = liftA2(A, B, fn (C) D, struct {
                fn curry2(a: A, b: B) fn (C) D {
                    return struct {
                        const val_a = a;
                        const val_b = b;
                        fn inner(c: C) D {
                            return func(val_a, val_b, c);
                        }
                    }.inner;
                }
            }.curry2, fa, fb);

            return apply(C, D, step1, fc);
        }
    };
}

/// Foldable 类型类
pub fn Foldable(comptime F: fn (type) type) type {
    return struct {
        /// foldl :: (b -> a -> b) -> b -> f a -> b
        pub fn foldl(comptime A: type, comptime B: type, comptime func: *const fn (B, A) B, initial: B, fa: F(A)) B {
            return fa.fold(B, initial, func);
        }

        /// foldr :: (a -> b -> b) -> b -> f a -> b
        pub fn foldr(comptime A: type, comptime B: type, comptime func: *const fn (A, B) B, initial: B, fa: F(A)) B {
            return fa.foldRight(B, initial, func);
        }

        /// foldMap :: Monoid m => (a -> m) -> f a -> m
        pub fn foldMap(comptime A: type, comptime M: type, comptime func: *const fn (A) M, comptime mempty: M, comptime mappend: *const fn (M, M) M, fa: F(A)) M {
            return foldl(A, M, struct {
                fn combine(acc: M, a: A) M {
                    return mappend(acc, func(a));
                }
            }.combine, mempty, fa);
        }

        /// length :: f a -> Int
        pub fn length(comptime A: type, fa: F(A)) usize {
            return foldl(A, usize, struct {
                fn count(acc: usize, _: A) usize {
                    return acc + 1;
                }
            }.count, 0, fa);
        }

        /// isEmpty :: f a -> Bool
        pub fn isEmpty(comptime A: type, fa: F(A)) bool {
            return length(A, fa) == 0;
        }

        /// elem :: Eq a => a -> f a -> Bool
        pub fn elem(comptime A: type, comptime eq_func: *const fn (A, A) bool, comptime target: A, fa: F(A)) bool {
            return foldl(A, bool, struct {
                fn check(found: bool, a: A) bool {
                    return found or eq_func(target, a);
                }
            }.check, false, fa);
        }

        /// sum :: Num a => f a -> a
        pub fn sum(comptime A: type, comptime add_func: *const fn (A, A) A, comptime zero: A, fa: F(A)) A {
            return foldl(A, A, add_func, zero, fa);
        }

        /// product :: Num a => f a -> a
        pub fn product(comptime A: type, comptime mul_func: *const fn (A, A) A, comptime one: A, fa: F(A)) A {
            return foldl(A, A, mul_func, one, fa);
        }
    };
}

/// Traversable 类型类
pub fn Traversable(comptime T: fn (type) type) type {
    return struct {
        /// traverse :: Applicative f => (a -> f b) -> t a -> f (t b)
        pub fn traverse(comptime F: fn (type) type, comptime A: type, comptime B: type, allocator: Allocator, comptime func: *const fn (A) F(B), ta: T(A)) !F(T(B)) {
            // 简化实现：假设 T 是列表类型
            _ = allocator;
            _ = func;
            _ = ta;

            // 这里需要根据具体的 T 类型来实现
            // 暂时返回错误，表示需要具体实现
            return error.NotImplemented;
        }

        /// sequence :: Applicative f => t (f a) -> f (t a)
        pub fn sequence(comptime F: fn (type) type, comptime A: type, allocator: Allocator, tfa: T(F(A))) !F(T(A)) {
            return traverse(F, F(A), A, allocator, struct {
                fn identity(fa: F(A)) F(A) {
                    return fa;
                }
            }.identity, tfa);
        }

        /// mapM :: Monad m => (a -> m b) -> t a -> m (t b)
        pub fn mapM(comptime M: fn (type) type, comptime A: type, comptime B: type, allocator: Allocator, comptime func: *const fn (A) M(B), ta: T(A)) !M(T(B)) {
            return traverse(M, A, B, allocator, func, ta);
        }

        /// forM :: Monad m => t a -> (a -> m b) -> m (t b)
        pub fn forM(comptime M: fn (type) type, comptime A: type, comptime B: type, allocator: Allocator, ta: T(A), comptime func: *const fn (A) M(B)) !M(T(B)) {
            return mapM(M, A, B, allocator, func, ta);
        }
    };
}

/// Maybe 的 Applicative 实例
pub const MaybeApplicative = struct {
    pub fn pure(comptime A: type, value: A) Maybe(A) {
        return Maybe(A).just(value);
    }

    pub fn apply(comptime A: type, comptime B: type, ff: Maybe(fn (A) B), fa: Maybe(A)) Maybe(B) {
        return switch (ff) {
            .Nothing => Maybe(B).nothing(),
            .Just => |func| fa.map(B, func),
        };
    }

    pub fn liftA2(comptime A: type, comptime B: type, comptime C: type, comptime func: *const fn (A, B) C, fa: Maybe(A), fb: Maybe(B)) Maybe(C) {
        return switch (fa) {
            .Nothing => Maybe(C).nothing(),
            .Just => |a| switch (fb) {
                .Nothing => Maybe(C).nothing(),
                .Just => |b| Maybe(C).just(func(a, b)),
            },
        };
    }

    pub fn sequence(comptime A: type, allocator: Allocator, maybes: []const Maybe(A)) !Maybe([]A) {
        var result = std.ArrayList(A).init(allocator);
        defer result.deinit();

        for (maybes) |maybe| {
            switch (maybe) {
                .Nothing => return Maybe([]A).nothing(),
                .Just => |value| try result.append(value),
            }
        }

        const owned_slice = try result.toOwnedSlice();
        return Maybe([]A).just(owned_slice);
    }

    pub fn traverse(comptime A: type, comptime B: type, allocator: Allocator, comptime func: *const fn (A) Maybe(B), items: []const A) !Maybe([]B) {
        var result = std.ArrayList(B).init(allocator);
        defer result.deinit();

        for (items) |item| {
            const maybe_b = func(item);
            switch (maybe_b) {
                .Nothing => return Maybe([]B).nothing(),
                .Just => |value| try result.append(value),
            }
        }

        const owned_slice = try result.toOwnedSlice();
        return Maybe([]B).just(owned_slice);
    }
};

/// 列表的 Foldable 实例
pub const ListFoldable = struct {
    pub fn foldl(comptime A: type, comptime B: type, comptime func: *const fn (B, A) B, initial: B, items: []const A) B {
        var acc = initial;
        for (items) |item| {
            acc = func(acc, item);
        }
        return acc;
    }

    pub fn foldr(comptime A: type, comptime B: type, comptime func: *const fn (A, B) B, initial: B, items: []const A) B {
        var acc = initial;
        var i = items.len;
        while (i > 0) {
            i -= 1;
            acc = func(items[i], acc);
        }
        return acc;
    }

    pub fn length(comptime A: type, items: []const A) usize {
        return items.len;
    }

    pub fn sum(comptime A: type, comptime add_func: *const fn (A, A) A, comptime zero: A, items: []const A) A {
        return foldl(A, A, add_func, zero, items);
    }

    pub fn product(comptime A: type, comptime mul_func: *const fn (A, A) A, comptime one: A, items: []const A) A {
        return foldl(A, A, mul_func, one, items);
    }

    pub fn elem(comptime A: type, comptime eq_func: *const fn (A, A) bool, comptime target: A, items: []const A) bool {
        return foldl(A, bool, struct {
            fn check(found: bool, a: A) bool {
                return found or eq_func(target, a);
            }
        }.check, false, items);
    }
};

// 测试
test "Maybe Applicative liftA2" {
    const add = struct {
        fn f(a: i32, b: i32) i32 {
            return a + b;
        }
    }.f;

    const maybe_5 = Maybe(i32).just(5);
    const maybe_3 = Maybe(i32).just(3);
    const maybe_nothing = Maybe(i32).nothing();

    const result1 = MaybeApplicative.liftA2(i32, i32, i32, add, maybe_5, maybe_3);
    try testing.expectEqual(@as(i32, 8), result1.getOrElse(0));

    const result2 = MaybeApplicative.liftA2(i32, i32, i32, add, maybe_5, maybe_nothing);
    try testing.expect(result2.isNothing());
}

test "Maybe sequence" {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();

    const maybes = [_]Maybe(i32){
        Maybe(i32).just(1),
        Maybe(i32).just(2),
        Maybe(i32).just(3),
    };

    const result = try MaybeApplicative.sequence(i32, allocator, &maybes);
    defer if (result.isJust()) allocator.free(result.getOrElse(&[_]i32{}));

    try testing.expect(result.isJust());
    const values = result.getOrElse(&[_]i32{});
    try testing.expectEqual(@as(usize, 3), values.len);
    try testing.expectEqual(@as(i32, 1), values[0]);
    try testing.expectEqual(@as(i32, 3), values[2]);
}

test "List Foldable" {
    const numbers = [_]i32{ 1, 2, 3, 4, 5 };

    const add = struct {
        fn f(a: i32, b: i32) i32 {
            return a + b;
        }
    }.f;

    const mul = struct {
        fn f(a: i32, b: i32) i32 {
            return a * b;
        }
    }.f;

    const eq = struct {
        fn f(a: i32, b: i32) bool {
            return a == b;
        }
    }.f;

    try testing.expectEqual(@as(usize, 5), ListFoldable.length(i32, &numbers));
    try testing.expectEqual(@as(i32, 15), ListFoldable.sum(i32, add, 0, &numbers));
    try testing.expectEqual(@as(i32, 120), ListFoldable.product(i32, mul, 1, &numbers));
    try testing.expect(ListFoldable.elem(i32, eq, 3, &numbers));
    try testing.expect(!ListFoldable.elem(i32, eq, 6, &numbers));
}
