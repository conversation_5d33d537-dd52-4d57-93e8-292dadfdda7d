#!/bin/bash

# 测试所有 Haskell 函数式编程特性的实现

echo "🧪 开始测试 Zig 中的 Haskell 函数式编程特性..."
echo

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
run_test() {
    local test_name="$1"
    local test_file="$2"
    
    echo -e "${YELLOW}测试: $test_name${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if zig test "$test_file" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $test_name 通过${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}❌ $test_name 失败${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        echo "运行 'zig test $test_file' 查看详细错误信息"
    fi
    echo
}

# 基础特性测试
echo "📚 基础函数式编程特性"
run_test "内存管理 (智能指针)" "src/memory_management.zig"
run_test "基础类型类 (Functor, Monad)" "src/typeclasses.zig"
echo

# 高级特性测试
echo "🚀 高级函数式编程特性"
run_test "惰性求值" "src/lazy_evaluation.zig"
run_test "高级类型类 (Applicative, Foldable)" "src/advanced_typeclasses.zig"
run_test "高级 Monad (State, Reader, Writer)" "src/advanced_monads.zig"
run_test "递归数据结构 (函数式链表和树)" "src/recursive_structures.zig"
run_test "Lens 和 Optics" "src/lens_optics.zig"
run_test "并发特性 (绿色线程, STM)" "src/concurrency.zig"
echo

# 总结
echo "📊 测试总结"
echo "================================"
echo -e "总测试数: $TOTAL_TESTS"
echo -e "${GREEN}通过: $PASSED_TESTS${NC}"
echo -e "${RED}失败: $FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 所有测试都通过了！${NC}"
    echo
    echo "🎯 已成功实现的 Haskell 特性："
    echo "  • 惰性求值和无限数据结构"
    echo "  • 高级类型类 (Applicative, Foldable, Traversable)"
    echo "  • 高级 Monad (State, Reader, Writer, IO)"
    echo "  • 递归数据结构 (函数式链表、树)"
    echo "  • Lens 和 Optics (函数式数据访问)"
    echo "  • 并发特性 (绿色线程、STM)"
    echo "  • 智能指针和内存管理"
    echo
    echo "💡 这些实现展示了如何在系统级语言中应用函数式编程思想！"
    exit 0
else
    echo -e "${RED}❌ 有 $FAILED_TESTS 个测试失败${NC}"
    echo "请检查失败的测试并修复问题。"
    exit 1
fi
